default:
    @just --list

dev:
    bun run concurrently \
        "just dev-server" \
        "just dev-css"

dev-server:
    bun --hot src/index.ts

dev-css:
    bun run tailwindcss -i ./src/web/app.css -o ./static/app.css --watch

build-css:
    bun run tailwindcss -i ./src/web/app.css -o ./static/app.css -m

start: build-css
    bun run src/index.ts

test:
    bun test --coverage

lint:
    bun run syncpack lint
    bun run prettier --check .
    bun run eslint .

fix:
    bun run syncpack fix-mismatches
    bun run syncpack format
    bun run prettier --write .
    bun run eslint --fix .

update:
    bun run syncpack update
    bun install

