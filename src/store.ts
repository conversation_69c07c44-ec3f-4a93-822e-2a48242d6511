import { logger } from "@/logger";

type RequestStatus = "pending" | "processing" | "completed" | "error";

type AgentRequest = {
  id: string;
  query: string;
  status: RequestStatus;
  result?: string;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
};

class RequestStore {
  private requests = new Map<string, AgentRequest>();

  public create(query: string): AgentRequest {
    const id = crypto.randomUUID();
    const request: AgentRequest = {
      id,
      query,
      status: "pending",
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.requests.set(id, request);
    logger.info("Created new request", { id, query });
    return request;
  }

  public get(id: string): AgentRequest | undefined {
    return this.requests.get(id);
  }

  public update(
    id: string,
    updates: Partial<AgentRequest>,
  ): AgentRequest | undefined {
    const request = this.requests.get(id);
    if (!request) return undefined;

    const updated = {
      ...request,
      ...updates,
      updatedAt: new Date(),
    };
    this.requests.set(id, updated);
    logger.info("Updated request", { id, status: updated.status });
    return updated;
  }

  public getAll(): AgentRequest[] {
    return Array.from(this.requests.values()).sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
    );
  }
}

export const requestStore = new RequestStore();
export type { RequestStatus, AgentRequest };
