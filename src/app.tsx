import { route as debugRoute } from "@/debug/route";
import { analyze } from "@/journey/agent";
import { logger } from "@/logger";
import { requestStore } from "@/store";
import { HomePage } from "@/web/home";
import { ResultCard } from "@/web/result";
import { Hono } from "hono";
import { serveStatic } from "hono/bun";
import { z } from "zod";

const app = new Hono();

app.use("/static/*", serveStatic({ root: "./" }));

app.get("/", (c) => {
  return c.html(<HomePage />);
});

const requestSchema = z.object({
  query: z.string().min(1).max(1000),
});

app.post("/api/request", async (c) => {
  try {
    const formData = await c.req.formData();
    const data = {
      query: formData.get("query") as string,
    };

    const parsed = requestSchema.parse(data);
    const request = requestStore.create(parsed.query);

    // Start processing asynchronously
    void processRequest(request.id);

    return await c.html(<ResultCard request={request} />);
  } catch (error) {
    logger.error("Failed to create request", error);
    return c.html(
      <div class="alert alert-error">
        <span>Invalid request. Please try again.</span>
      </div>,
      400,
    );
  }
});

app.get("/api/request/:id", (c) => {
  const id = c.req.param("id");
  const request = requestStore.get(id);

  if (!request) {
    return c.html(
      <div class="alert alert-error">
        <span>Request not found</span>
      </div>,
      404,
    );
  }

  return c.html(<ResultCard request={request} />);
});

async function processRequest(id: string) {
  try {
    requestStore.update(id, { status: "processing" });
    const request = requestStore.get(id);
    if (!request) return;

    const result = await analyze(request.query);
    requestStore.update(id, {
      status: "completed",
      result,
    });
  } catch (error) {
    logger.error("Failed to process request", { id, error });
    requestStore.update(id, {
      status: "error",
      error: error instanceof Error ? error.message : "An error occurred",
    });
  }
}

export { app };
