import { Layout } from "@/web/layout";

export function HomePage() {
  return (
    <Layout>
      <div class="max-w-4xl mx-auto">
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-2xl mb-4">Ask the Agent</h2>
            <form
              hx-post="/api/request"
              hx-target="#result-container"
              hx-swap="innerHTML"
              class="space-y-4 w-full"
            >
              <div class="form-control w-full">
                <textarea
                  name="query"
                  class="textarea textarea-bordered h-32"
                  placeholder="Dear AI..."
                  required
                ></textarea>
              </div>
              <div class="card-actions justify-end">
                <button type="submit" class="btn btn-primary">
                  Submit Request
                </button>
              </div>
            </form>
          </div>
        </div>
        <div id="result-container" class="mt-6"></div>
      </div>
    </Layout>
  );
}
