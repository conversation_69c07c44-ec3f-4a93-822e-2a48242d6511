import type { AgentRequest } from "@/store";

export function ResultCard({ request }: { request: AgentRequest }) {
  const statusBadge = {
    pending: "badge-info",
    processing: "badge-warning",
    completed: "badge-success",
    error: "badge-error",
  }[request.status];

  const isProcessing =
    request.status === "pending" || request.status === "processing";

  return (
    <div
      id={`result-${request.id}`}
      class="card bg-base-100 shadow-xl"
      {...(isProcessing && {
        "hx-get": `/api/request/${request.id}`,
        "hx-trigger": "every 1s",
        "hx-target": "this",
        "hx-swap": "outerHTML",
      })}
    >
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title">Request Status</h3>
          <div class={`badge ${statusBadge}`}>{request.status}</div>
        </div>

        <div class="space-y-4">
          <div>
            <p class="text-sm text-base-content/70">Query:</p>
            <p class="font-medium">{request.query}</p>
          </div>

          {isProcessing && (
            <div class="flex items-center space-x-2">
              <span class="loading loading-spinner loading-sm"></span>
              <span>Processing your request...</span>
            </div>
          )}

          {request.status === "completed" && request.result && (
            <div>
              <p class="text-sm text-base-content/70 mb-2">Result:</p>
              <div class="bg-base-200 p-4 rounded-lg">
                <div class="typography">{request.result}</div>
              </div>
            </div>
          )}

          {request.status === "error" && request.error && (
            <div class="alert alert-error">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="stroke-current shrink-0 h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>{request.error}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
