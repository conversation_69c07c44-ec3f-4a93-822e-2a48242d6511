import type { Child } from "hono/jsx";

export function Layout({ children }: { children: Child }) {
  return (
    <html lang="en" data-theme="light">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Agent Assistant</title>
        <link rel="stylesheet" href="/static/app.css" />
        <script src="https://unpkg.com/htmx.org@2.0.4"></script>
      </head>
      <body>
        <div class="min-h-screen bg-base-200">
          <div class="navbar bg-base-100 shadow-lg">
            <div class="flex-1">
              <a href="/" class="btn btn-ghost text-xl">
                Agent Assistant
              </a>
            </div>
          </div>
          <main class="container mx-auto p-4">{children}</main>
        </div>
      </body>
    </html>
  );
}
