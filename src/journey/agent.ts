import { logger } from "@/logger.ts";
import { Agent, MCPServerStdio, Runner } from "@openai/agents";
import { aisdk } from "@openai/agents-extensions";
import { openrouter } from "@openrouter/ai-sdk-provider";

const model = aisdk(
  openrouter("google/gemini-2.5-flash-preview-05-20"),
  // openrouter("google/gemini-2.5-flash-preview-05-20:thinking"),
);
const modelSettings = {
  providerData: {
    reasoning: {
      effort: "high",
      summary: "auto",
    },
  },
};

const playwrightMcp = new MCPServerStdio({
  fullCommand: "npx -y @playwright/mcp@latest --headless",
});

const agent = new Agent({
  model,
  modelSettings,
  name: "Browser Agent",
  instructions:
    "You are a Web UX expert, you know how to work a browser to answer the user's questions. Use the tools provided to navigate the web and comprehensively answer the user's questions.",
  mcpServers: [playwrightMcp],
});

export async function analyze(request: string): Promise<string> {
  await playwrightMcp.connect();
  try {
    const runner = new Runner({ workflowName: "UX Assistant" });
    const result = await runner.run(agent, request);
    return result.finalOutput ?? "Didn't get a response.";
  } catch (e: unknown) {
    logger.error("Couldn't run the agent", e);
    throw e;
  } finally {
    await playwrightMcp.close();
  }
}
