# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with
code in this repository.

## Commands

### Development

- `just dev` - Run the application in development mode with watch
- `just start` - Run the application
- `just test` - Run tests with coverage

### Code Quality

- `just lint` - Run all linters (syncpack, prettier, eslint)
- `just fix` - Fix all auto-fixable issues (syncpack, prettier, eslint)
- `just update` - Update dependencies via syncpack

## Architecture

This is a TypeScript project using:

- **Bun** as the runtime and package manager
- **Hono** for building lightweight, type-safe APIs
- **Zod** for runtime validation
- **tslog** for structured logging

The project follows a modular architecture with:

- Entry point at `src/index.ts`
- Environment configuration in `src/env.ts`
- Logging setup in `src/logger.ts`
- Routes defined in `src/app.tsx` and subdirectories (e.g.,
  `src/debug/route.tsx`)

## Development Guidelines

### Import Paths

Use absolute imports with the `@/` prefix (configured in tsconfig.json):

```typescript
import { logger } from "@/logger";
```

### TypeScript Configuration

The project uses strict TypeScript settings including:

- `noUncheckedIndexedAccess`
- `noPropertyAccessFromIndexSignature`
- `noUnusedLocals` and `noUnusedParameters`

### Testing

Tests are colocated with source files using the `.test.ts` extension.

### Documentation Lookup

When working with external libraries (Hono, Zod, tslog), use the context7 tool
to lookup their documentation:

1. First call `mcp__context7__resolve-library-id` with the library name
2. Then call `mcp__context7__get-library-docs` with the resolved ID
