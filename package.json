{"name": "typescript-experiments", "dependencies": {"@openai/agents": "0.0.6", "@openai/agents-extensions": "0.0.6", "@openrouter/ai-sdk-provider": "0.7.1", "hono": "4.7.11", "tslog": "4.9.3", "zod": "3.25.57"}, "devDependencies": {"@eslint/js": "9.28.0", "@ianvs/prettier-plugin-sort-imports": "4.4.2", "@tailwindcss/cli": "4.1.9", "@tailwindcss/typography": "0.5.16", "@types/bun": "latest", "concurrently": "9.1.2", "daisyui": "5.0.43", "eslint": "9.28.0", "eslint-config-prettier": "10.1.5", "eslint-import-resolver-typescript": "4.4.3", "eslint-plugin-import-x": "4.15.1", "eslint-plugin-no-relative-import-paths": "1.6.1", "prettier": "3.5.3", "prettier-plugin-sql": "0.19.1", "syncpack": "13.0.4", "tailwindcss": "4", "typescript-eslint": "8.34.0"}, "module": "index.ts", "peerDependencies": {"typescript": "^5"}, "private": true, "trustedDependencies": ["unrs-resolver"], "type": "module"}